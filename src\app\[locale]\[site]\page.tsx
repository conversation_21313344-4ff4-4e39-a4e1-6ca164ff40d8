import { i18nConfig } from '@/config/global'
import { siteMetadatas } from '@/config/downloader'
import { pick } from 'lodash'
import { NextIntlClientProvider, useMessages } from 'next-intl'
import { Extractor } from '@/components/client/extractor'
import { notFound, permanentRedirect } from 'next/navigation'
import { Locale, siteConfig } from '@/config/global'
import { get } from 'lodash'
import type { Metadata } from 'next'
import { SiteID, FAQ } from '@/config/downloader'
import { genPageMetadata } from '@/lib/seo'
import { defaultFAQ } from '../page'
import { PartialRecord } from '@/types/global'
import { FAQSection } from '@/components/faq-section'
import { AppPromotionBanner } from '@/components/client/app-promotion-banner'

const howToUses: Record<Locale, Record<SiteID, FAQ>> = {
  en: {
    tiktok: {
      question: 'How to download TikTok video/photo/note/foto without watermark?',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Open the TikTok app on your phone or on the Web in your browser.</li>
          <li>Choose whatever video, photo, or video you want to download.</li>
          <li>Click on the Share button at the right bottom.</li>
          <li>Click the Copy Link button. If you are on the website, copy the URL link directly.</li>
          <li>
            Go back to {siteConfig.name} and paste your share link in the field above, then click the Download button.
          </li>
          <li>Wait for our server to do its job, and then save the media file to your device.</li>
        </ol>
      ),
    },
    bilibili: {
      question: 'How to download bilibili video?',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Open the Bilibili app on your phone or on the Web in your browser.</li>
          <li>Choose whatever video you want to download. Then copy the sharing link for the video</li>
          <li>
            Go back to {siteConfig.name} and paste your share link in the field above, then click the Download button.
          </li>
          <li>Wait for our server to do its job, and then save the video to your device.</li>
        </ol>
      ),
    },
    vk: {
      question: 'How do I download VK social network videos on a PC, laptop, or phone?',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Open the VK(VKontakte) app on your phone or on the Web in your browser.</li>
          <li>Choose whatever video you want to download. Then copy the sharing link for the video.</li>
          <li>
            Go back to {siteConfig.name} and paste your share link in the field above, then click the Download button.
          </li>
          <li>Wait for our server to do its job, and then save the video to your device.</li>
        </ol>
      ),
    },
    // snapchat: {
    //   question: 'How do I download Snapchat Stories, Lenses, and Spotlight Videos?',
    //   answer: <ol className="list-decimal list-inside text-sm my-2">
    //     <li>Open the Snapchat app on your phone or on the Web in your browser.</li>
    //     <li>Choose whatever video you want to download. Then copy the sharing link for the video.</li>
    //     <li>Go back to {siteConfig.name} and paste your share link in the field above, then click the Download button.</li>
    //     <li>Wait for our server to do its job, and then save the video to your device.</li>
    //   </ol>
    // },
    threads: {
      question: 'How to download Threads videos or photos?',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Open the Threads app on your phone or on the Web in your browser.</li>
          <li>Choose whatever videos or photos you want to download. Then copy the thread-sharing link.</li>
          <li>
            Go back to {siteConfig.name} and paste your share link in the field above, then click the Download button.
          </li>
          <li>Wait for our server to do its job, and then save the media file to your device.</li>
        </ol>
      ),
    },
    pinterest: {
      question: 'How to download Pinterest videos or images? (Quick Guide)',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Open the Pinterest app on your phone or on the Web in your browser.</li>
          <li>Choose whatever videos or images you want to download. Then copy the sharing link.</li>
          <li>
            Go back to {siteConfig.name} and paste your share link in the field above, then click the Download button.
          </li>
          <li>Wait for our server to do its job, and then save the media file to your device.</li>
        </ol>
      ),
    },
    facebook: {
      question: 'How to download Facebook HD videos, reels, or stories?',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Open the Facebook app on your phone or on the Web in your browser.</li>
          <li>Choose whatever videos, reels, or stories you want to download. Then copy the sharing link.</li>
          <li>
            Go back to {siteConfig.name} and paste your share link in the field above, then click the Download button.
          </li>
          <li>Wait for our server to do its job, and then save the media file to your device.</li>
        </ol>
      ),
    },
    suno: {
      question: 'How to download the Suno AI Music Song in MP3 format?',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Open the Suno website.</li>
          <li>Click on the share button above any song to obtain the share URL.</li>
          <li>
            Go back to {siteConfig.name} and paste the URL you just obtained into the input box above, then click the
            Download button.
          </li>
        </ol>
      ),
    },
    youtube: {
      question: 'How to Download and Save YouTube HD Videos?',
      answer: (<ol className="list-decimal list-inside">
        <li>Open the YouTube website or app, find the video you want to download, and save it.</li>
        <li>Click the "Share" button below the video, then select "Copy Link."</li>
        <li>Open the {siteConfig.name} website and paste the copied YouTube video link into the input box on the {siteConfig.name} page.</li>
        <li>Click the "Download" button and wait for a few seconds; save the HD video resolution and format you prefer.</li>
      </ol>),
    }
  },
  zh: {
    tiktok: {
      question: '如何下载保存抖音/TikTok无水印视频图片?',
      answer: (
        <ol className="list-decimal list-inside">
          <li>打开抖音/TikTok网站或APP，找到想要下载保存的视频/图片，点击分享按钮，点击复制链接</li>
          <li>
            回到{siteConfig.name}
            ，将刚才复制的链接粘贴到上面的输入框，点击解析按钮，稍等几秒，解析成功后会返回高清视频和图片，点击下载即可
          </li>
        </ol>
      ),
    },
    bilibili: {
      question: '如何下载保存bilibili视频？',
      answer: (
        <ol className="list-decimal list-inside">
          <li>在哔哩哔哩(bilibili、B站) APP或网站上，找到想要下载的视频，复制视频页面链接</li>
          <li>
            回到{siteConfig.name}
            ，将刚才复制的链接粘贴到上面的输入框，点击解析按钮，稍等几秒，解析成功后会返回高清视频和图片，点击下载即可
          </li>
        </ol>
      ),
    },
    vk: {
      question: '如何下载保存VK俄罗斯社交网络视频？',
      answer: (
        <ol className="list-decimal list-inside">
          <li>在VK(VKontakte) APP或网站上，找到想要下载的视频，复制视频页面链接</li>
          <li>
            回到{siteConfig.name}
            ，将刚才复制的链接粘贴到上面的输入框，点击解析按钮，稍等几秒，解析成功后会返回高清视频和图片，点击下载即可
          </li>
        </ol>
      ),
    },
    threads: {
      question: '如何下载保存Threads视频图片？',
      answer: (
        <ol className="list-decimal list-inside">
          <li>在Threads APP或网站上，找到想要下载的视频图片，复制帖子链接</li>
          <li>
            回到{siteConfig.name}
            ，将刚才复制的链接粘贴到上面的输入框，点击解析按钮，稍等几秒，解析成功后会返回高清视频和图片，点击下载即可
          </li>
        </ol>
      ),
    },
    pinterest: {
      question: '如何下载保存Pinterest视频图片？',
      answer: (
        <ol className="list-decimal list-inside">
          <li>在Pinterest APP或网站上，找到想要下载的视频图片，复制分享链接</li>
          <li>
            回到{siteConfig.name}
            ，将刚才复制的链接粘贴到上面的输入框，点击解析按钮，稍等几秒，解析成功后会返回高清视频和图片，点击下载即可
          </li>
        </ol>
      ),
    },
    facebook: {
      question: '如何下载保存Facebook社交视频？',
      answer: (
        <ol className="list-decimal list-inside">
          <li>在Facebook(脸书) APP或网站上，找到想要下载的视频，复制视频页面链接</li>
          <li>
            回到{siteConfig.name}
            ，将刚才复制的链接粘贴到上面的输入框，点击解析按钮，稍等几秒，解析成功后会返回高清视频和图片，点击下载即可
          </li>
        </ol>
      ),
    },
    suno: {
      question: '如何将SunoAI平台创作的歌曲以MP3格式下载到本地?',
      answer: (
        <ol className="list-decimal list-inside">
          <li>在浏览器中打开Suno AI网站，找到想要下载保存的歌曲，点击分享按钮获取歌曲的分享链接</li>
          <li>
            回到{siteConfig.name}
            ，将刚才复制的链接粘贴到输入框，点击提取按钮，稍等几秒，得到歌曲的MP3下载按钮，点击下载即可
          </li>
        </ol>
      ),
    },
    youtube: {
      question: '如何下载保存YouTube(油管)高清视频？',
      answer: (
        <ol className="list-decimal list-inside">
          <li>打开YouTube网站或APP，找到您想要下载保存的高清视频。点击视频下方的“分享”按钮，然后点击"复制链接"。</li>
          <li>将刚才复制的YouTube视频链接粘贴到{siteConfig.name}页面上的输入框中。点击"提取视频图片"按钮，稍等几秒，您可以选择高清视频的不同分辨率和格式。</li>
          <li>点击"下载"按钮，视频将保存到您的设备中。</li>
        </ol>
      )
    }
  },
  ja: {
    tiktok: {
      question: 'ウォーターマークなしでTikTokの動画/写真/ノート/フォトをダウンロードする方法は？',
      answer: (
        <ol className="list-decimal list-inside">
          <li>携帯電話またはブラウザのWebでTikTokアプリを開きます。</li>
          <li>ダウンロードしたい動画、写真、またはビデオを選択します。</li>
          <li>右下にある共有ボタンをクリックします。</li>
          <li>リンクをコピーするボタンをクリックします。ウェブサイトにいる場合は、URLリンクを直接コピーします。</li>
          <li>
            {siteConfig.name}に戻り、共有リンクを上記のフィールドに貼り付けてから、ダウンロードボタンをクリックします。
          </li>
          <li>私たちのサーバーが作業を行い、その後、メディアファイルをデバイスに保存します。</li>
        </ol>
      ),
    },
    bilibili: {
      question: 'bilibiliの動画をダウンロードする方法は？',
      answer: (
        <ol className="list-decimal list-inside">
          <li>携帯電話またはブラウザのWebでBilibiliアプリを開きます。</li>
          <li>ダウンロードしたい動画を選択し、その動画の共有リンクをコピーします。</li>
          <li>
            {siteConfig.name}に戻り、共有リンクを上記のフィールドに貼り付けてから、ダウンロードボタンをクリックします。
          </li>
          <li>私たちのサーバーが作業を行い、その後、動画をデバイスに保存します。</li>
        </ol>
      ),
    },
    vk: {
      question: 'PC、ラップトップ、または携帯電話でVKソーシャルネットワークの動画をダウンロードする方法は？',
      answer: (
        <ol className="list-decimal list-inside">
          <li>携帯電話またはブラウザのWebでVK（VKontakte）アプリを開きます。</li>
          <li>ダウンロードしたい動画を選択し、その動画の共有リンクをコピーします。</li>
          <li>
            {siteConfig.name}に戻り、共有リンクを上記のフィールドに貼り付けてから、ダウンロードボタンをクリックします。
          </li>
          <li>私たちのサーバーが作業を行い、その後、動画をデバイスに保存します。</li>
        </ol>
      ),
    },
    threads: {
      question: 'Threadsのビデオや写真をダウンロードする方法は？',
      answer: (
        <ol className="list-decimal list-inside">
          <li>携帯電話またはブラウザでThreadsアプリを開きます。</li>
          <li>ダウンロードしたいビデオや写真を選択し、そのスレッド共有リンクをコピーします。</li>
          <li>
            {siteConfig.name}
            に戻り、上記のフィールドに共有リンクを貼り付けてから、「ダウンロード」ボタンをクリックします。
          </li>
          <li>私たちのサーバーが作業を行い、その後、メディアファイルをデバイスに保存します。</li>
        </ol>
      ),
    },
    pinterest: {
      question: 'Pinterestの動画や画像をダウンロードする方法？（クイックガイド）',
      answer: (
        <ol className="list-decimal list-inside">
          <li>携帯電話またはブラウザでPinterestアプリを開きます。</li>
          <li>ダウンロードしたい動画や画像を選択し、共有リンクをコピーします。</li>
          <li>
            {siteConfig.name}
            に戻り、上記のフィールドに共有リンクを貼り付けてから、「ダウンロード」ボタンをクリックします。
          </li>
          <li>私たちのサーバーが作業を行い、その後、メディアファイルをデバイスに保存します。</li>
        </ol>
      ),
    },
    facebook: {
      question: 'Facebookのソーシャルビデオをダウンロードして保存する方法は？',
      answer: (
        <ol className="list-decimal list-inside">
          <li>携帯電話またはブラウザでFacebookアプリを開きます。</li>
          <li>ダウンロードしたい動画、リール、またはストーリーを選択し、共有リンクをコピーします。</li>
          <li>
            {siteConfig.name}
            に戻り、上記のフィールドに共有リンクを貼り付けてから、「ダウンロード」ボタンをクリックします。
          </li>
          <li>私たちのサーバーが作業を行い、その後、メディアファイルをデバイスに保存します。</li>
        </ol>
      ),
    },
    suno: {
      question: 'Suno AI音楽ソングをMP3形式でダウンロードする方法は？',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Sunoのウェブサイトを開きます。</li>
          <li>任意の曲の上にあるシェアボタンをクリックして、シェアURLを取得します。</li>
          <li>
            {siteConfig.name}
            に戻って、先ほど取得したURLを上の入力ボックスに貼り付け、ダウンロードボタンをクリックします。
          </li>
        </ol>
      ),
    },
    youtube: {
      question: 'PC、ラップトップ、または携帯電話でYouTubeのHD動画をダウンロードする方法は？',
      answer: (
        <ol className="list-decimal list-inside">
          <li>YouTubeのウェブサイトまたはアプリを開き、ダウンロードしたい動画を見つけます。</li>
          <li>動画の下にある「共有」ボタンをクリックし、「リンクをコピー」を選択します。</li>
          <li>{siteConfig.name}のウェブサイトを開き、コピーしたYouTube動画リンクを{siteConfig.name}ページの入力ボックスに貼り付けます。</li>
          <li>「ダウンロード」ボタンをクリックし、数秒待ってから、希望のHD動画の解像度とフォーマットを選択して保存します。</li>
        </ol>
      ),
    }
  },
  es: {
    tiktok: {
      question: '¿Cómo descargar un video/foto/nota/foto de TikTok sin marca de agua?',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Abre la aplicación de TikTok en tu teléfono o en la Web desde tu navegador.</li>
          <li>Elige el video, foto o nota que quieras descargar.</li>
          <li>Haz clic en el botón Compartir en la parte inferior derecha.</li>
          <li>Haz clic en el botón Copiar Enlace. Si estás en la página web, copia directamente el enlace URL.</li>
          <li>
            Regresa a {siteConfig.name} y pega tu enlace compartido en el campo de arriba, luego haz clic en el botón
            Descargar.
          </li>
          <li>Espera a que nuestro servidor haga su trabajo y luego guarda el archivo multimedia en tu dispositivo.</li>
        </ol>
      ),
    },
    bilibili: {
      question: '¿Cómo descargar videos de bilibili?',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Abre la aplicación de Bilibili en tu teléfono o en la Web desde tu navegador.</li>
          <li>Elige el video que quieras descargar. Luego copia el enlace de compartición del video.</li>
          <li>
            Regresa a {siteConfig.name} y pega tu enlace compartido en el campo de arriba, luego haz clic en el botón
            Descargar.
          </li>
          <li>Espera a que nuestro servidor haga su trabajo y luego guarda el video en tu dispositivo.</li>
        </ol>
      ),
    },
    vk: {
      question: '¿Cómo descargar videos de la red social VK en una PC, portátil o teléfono?',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Abre la aplicación de VK (VKontakte) en tu teléfono o en la Web desde tu navegador.</li>
          <li>Elige el video que quieras descargar. Luego copia el enlace de compartición del video.</li>
          <li>
            Regresa a {siteConfig.name} y pega tu enlace compartido en el campo de arriba, luego haz clic en el botón
            Descargar.
          </li>
          <li>Espera a que nuestro servidor haga su trabajo y luego guarda el video en tu dispositivo.</li>
        </ol>
      ),
    },
    threads: {
      question: '¿Cómo descargar videos o fotos de Threads?',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Abre la aplicación de Threads en tu teléfono o en la Web desde tu navegador.</li>
          <li>Elige los videos o fotos que quieras descargar. Luego copia el enlace de compartición del hilo.</li>
          <li>
            Regresa a {siteConfig.name} y pega tu enlace compartido en el campo de arriba, luego haz clic en el botón
            Descargar.
          </li>
          <li>Espera a que nuestro servidor haga su trabajo y luego guarda el archivo multimedia en tu dispositivo.</li>
        </ol>
      ),
    },
    pinterest: {
      question: '¿Cómo descargar videos o imágenes de Pinterest? (Guía rápida)',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Abre la aplicación de Pinterest en tu teléfono o en la Web desde tu navegador.</li>
          <li>Elige los videos o imágenes que quieras descargar. Luego copia el enlace de compartición.</li>
          <li>
            Regresa a {siteConfig.name} y pega tu enlace compartido en el campo de arriba, luego haz clic en el botón
            Descargar.
          </li>
          <li>Espera a que nuestro servidor haga su trabajo y luego guarda el archivo multimedia en tu dispositivo.</li>
        </ol>
      ),
    },
    facebook: {
      question: '¿Cómo descargar videos HD, reels o historias de Facebook?',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Abre la aplicación de Facebook en tu teléfono o en la Web desde tu navegador.</li>
          <li>Elige los videos, reels o historias que quieras descargar. Luego copia el enlace de compartición.</li>
          <li>
            Regresa a {siteConfig.name} y pega tu enlace compartido en el campo de arriba, luego haz clic en el botón
            Descargar.
          </li>
          <li>Espera a que nuestro servidor haga su trabajo y luego guarda el archivo multimedia en tu dispositivo.</li>
        </ol>
      ),
    },
    suno: {
      question: '¿Cómo descargar la canción de Suno AI Music en formato MP3?',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Abre la página web de Suno.</li>
          <li>Haz clic en el botón de compartir encima de cualquier canción para obtener el URL de compartición.</li>
          <li>
            Vuelve a {siteConfig.name} y pega el URL que acabas de obtener en el cuadro de entrada de arriba, luego haz
            clic en el botón de Descargar.
          </li>
        </ol>
      ),
    },
    youtube: {
      question: '¿Cómo descargar y guardar videos HD de YouTube?',
      answer: (
        <ol className="list-decimal list-inside">
          <li>Abre el sitio web o la aplicación de YouTube, encuentra el video que deseas descargar y guardar.</li>
          <li>Haz clic en el botón "Compartir" debajo del video, luego selecciona "Copiar enlace".</li>
          <li>Abre el sitio web de {siteConfig.name} y pega el enlace del video de YouTube copiado en el cuadro de entrada en la página de {siteConfig.name}.</li>
          <li>Haz clic en el botón "Descargar" y espera unos segundos; guarda la resolución y el formato de video HD que prefieras.</li>
        </ol>
      ),
    }
  },
}


// option
const faqs: PartialRecord<Locale, PartialRecord<SiteID, FAQ[]>> = {
  en: {
    tiktok: [
      {
        question: `Can I download high-resolution TikTok videos at ${siteConfig.name}?`,
        answer: `${siteConfig.name} provides the highest resolution for you. If we find a higher resolution of a TikTok video, we will immediately show a higher-quality download link, and you can download it.`,
      },
    ],
    youtube: [
      {
        question: `Can I download videos in different formats using ${siteConfig.name}?`,
        answer: `${siteConfig.name} allows you to download videos in various formats, such as MP4, WEBM, and 3GP.`
      }
    ],
  },
}



export async function generateMetadata({
  params,
}: {
  params: { locale: Locale; site: SiteID | 'snapchat' }
}): Promise<Metadata> {
  if (params.site === 'snapchat') {
    permanentRedirect(`/`)
  }

  const siteMetadata = get(siteMetadatas, [params.locale, params.site])
  if (!siteMetadata) {
    notFound()
  }

  return genPageMetadata({
    title: siteMetadata.title,
    description: siteMetadata.description,
    pathname: params.site,
    locale: params.locale,
    applicationName: siteMetadata.heading,
  })
}

export default function Page({ params }: { params: { locale: Locale; site: SiteID | 'snapchat' } }) {
  if (params.site === 'snapchat') {
    permanentRedirect(`/`)
  }

  const siteMetadata = get(siteMetadatas, [params.locale, params.site])
  if (!siteMetadata) {
    notFound()
  }

  const howToUse = howToUses[params.locale][params.site] ?? howToUses[i18nConfig.defaultLocale][params.site]
  const faq = [
    {
      question: howToUse.question,
      answer: howToUse.answer,
    },
    ...get(faqs, [params.locale, params.site], []),
    ...defaultFAQ[params.locale],
  ]
  const messages = useMessages()

  return (
    <main className="mb-8">
      <section className="bg-white dark:bg-gray-900 bg-[url('/images/hero-pattern.svg')] dark:bg-[url('/images/hero-pattern-dark.svg')]">
        <div className="bg-gradient-to-b from-blue-50 to-transparent dark:from-blue-900 w-full h-full">
          <div className="container flex flex-col items-center py-12 sm:py-16 gap-4">
            <AppPromotionBanner />
            <div className="flex flex-col gap-4 items-center text-center">
              <h1 className="text-gray-900 tracking-tight text-3xl sm:text-4xl lg:text-5xl font-bold">
                {siteMetadata.heading}
              </h1>
              {siteMetadata.subheading && (
                <h2 className="text-gray-500 text-lg lg:text-xl">{siteMetadata.subheading}</h2>
              )}
            </div>
            <NextIntlClientProvider messages={pick(messages, ['extractor.client', 'appDownloadButtons'])}>
              <Extractor />
            </NextIntlClientProvider>
          </div>
        </div>
      </section>
      <FAQSection faq={faq} />
    </main>
  )
}
