import { notFound } from 'next/navigation'
import { Locale, siteConfig } from '@/config/global'
import type { Metadata } from 'next'
import { genPageMetadata } from '@/lib/seo'
import { logger } from '@gongyinshi/kit/logger'
import { MILLISECONDS_IN_SECOND } from '@gongyinshi/common/datetime'
import { Article } from '@/types/global'
import { ArticleBlocksRenderer } from '@/components/client/article-blocks-renderer'
import { AppPromotionBanner } from '@/components/client/app-promotion-banner'

async function getData(locale: Locale, slug: string): Promise<{ article: Article; availableLocales: Locale[] }> {
  try {
    const res = await fetch(
      `${process.env.CMS_API_BASE_URL}/api/articles?filters[slug][$eq]=${slug}&locale=${locale}&populate[localizations][fields]=locale`,
      {
        method: 'GET',
        headers: {
          Authorization: `bearer ${process.env.CMS_API_TOKEN}`,
        },
        signal: AbortSignal.timeout(30 * MILLISECONDS_IN_SECOND),
        next: { tags: ['articles'] },
      }
    )
    const j = await res.json()
    const article = j.data[0]
    if (!article) {
      throw new Error('not found')
    }
    const availableLocales = [article.locale, ...article.localizations.map((item: any) => item.locale)]
    return { article, availableLocales }
  } catch (e) {
    logger.error(e, `article not found: ${locale}/${slug}`)
    notFound()
  }
}

export async function generateMetadata({ params }: { params: { locale: Locale; slug: string } }): Promise<Metadata> {
  const { article, availableLocales } = await getData(params.locale, params.slug)

  return genPageMetadata({
    title: `${article.title} - ${siteConfig.name}`,
    description: article.description,
    pathname: `article/${params.slug}`,
    locale: params.locale,
    availableLocales,
    type: 'article',
  })
}

export default async function Page({ params }: { params: { locale: Locale; slug: string } }) {
  const { article } = await getData(params.locale, params.slug)

  return (
    <main>
      <section className="bg-white dark:bg-gray-900 bg-[url('/images/hero-pattern.svg')] dark:bg-[url('/images/hero-pattern-dark.svg')]">
        <div className="bg-gradient-to-b from-blue-50 to-transparent dark:from-blue-900 w-full h-full">
          <div className="container flex flex-col items-center py-12 sm:py-16 gap-4">
            <AppPromotionBanner />
            <h1 className="text-center text-3xl leading-8 font-extrabold tracking-tight text-gray-900 md:text-4xl">
              {article.title}
            </h1>
          </div>
        </div>
      </section>
      <article className="container max-w-3xl pb-12 sm:pb-16 prose lg:prose-lg">
        <div>
          <ArticleBlocksRenderer content={article.content} />
        </div>
      </article>
    </main>
  )
}
