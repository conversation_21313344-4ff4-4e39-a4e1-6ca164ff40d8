'use client'

import { useTranslations } from 'next-intl'
import { <PERSON> } from '@/navigation'
import { Al<PERSON>, Badge } from 'flowbite-react'
import { AngleRight } from 'flowbite-react-icons/outline'

interface AppPromotionBannerProps {
  /**
   * The href for the promotion link
   * @default '/app'
   */
  href?: string
  /**
   * Additional CSS classes for the container
   * @default 'w-full max-w-4xl flex justify-center'
   */
  className?: string
}

/**
 * A reusable banner component that promotes the new desktop app
 * Displays a clickable alert with "New" badge and promotional message
 */
export function AppPromotionBanner({ 
  href = '/app', 
  className = 'w-full max-w-4xl flex justify-center' 
}: AppPromotionBannerProps) {
  const t = useTranslations('newVersionAlert')
  
  return (
    <Link href={href} className={className}>
      <Alert
        color="info"
        className="border-0 bg-transparent cursor-pointer p-0"
        additionalContent={
          <div className="pl-1 pr-2 py-1 bg-white hover:bg-gray-50 rounded-full inline-flex justify-start items-center gap-2 transition-colors">
            <Badge className="px-2 py-1 bg-primary-700 rounded-full flex justify-start items-center gap-1">
              <div className="text-center justify-start text-white text-xs font-medium leading-none">New</div>
            </Badge>
            <div className="flex justify-start items-center gap-2">
              <div className="justify-start text-gray-600 text-sm font-normal leading-tight">{t('message')}</div>
              <div className="w-4 h-4 flex items-center justify-center">
                <AngleRight className="w-4 h-4 text-gray-600" />
              </div>
            </div>
          </div>
        }
      />
    </Link>
  )
}
